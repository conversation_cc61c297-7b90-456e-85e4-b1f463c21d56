<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Iniciar sesión
session_start();

// Verificar autenticación y permisos de administrador
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    header('Content-Type: text/plain');
    echo "Usuario no autenticado o sin permisos para InteletGroup.";
    exit;
}

// Incluir conexión a base de datos
try {
    require_once 'con_db.php';
} catch (Exception $e) {
    header('Content-Type: text/plain');
    echo "Error de conexión a la base de datos: " . $e->getMessage();
    exit;
}

// Verificar conexión a base de datos
if (!isset($mysqli) || $mysqli->connect_error) {
    header('Content-Type: text/plain');
    echo "Error de conexión a la base de datos.";
    exit;
}

// Obtener parámetros de filtros desde GET o POST
$request_data = array_merge($_GET, $_POST);
$filtro_ejecutivo = $request_data['ejecutivo'] ?? 'todos';
$filtro_periodo = $request_data['periodo'] ?? 'año';
$filtro_fecha_inicio = $request_data['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $request_data['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Consulta simplificada para obtener prospectos
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    WHERE " . $where_clause . "
    ORDER BY p.fecha_registro DESC";

try {
    $stmt = $mysqli->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparando consulta: " . $mysqli->error);
    }

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    if (!$stmt->execute()) {
        throw new Exception("Error ejecutando consulta: " . $stmt->error);
    }

    // Usar bind_result() para compatibilidad con PHP 7.3.33
    $id = $tipo_persona = $rut_cliente = $razon_social = $rubro = $email = $telefono_celular = $fecha_registro = $ejecutivo_nombre_usuario = null;
    
    $stmt->bind_result($id, $tipo_persona, $rut_cliente, $razon_social, $rubro, $email, $telefono_celular, $fecha_registro, $ejecutivo_nombre_usuario);
    
    // Crear array para almacenar resultados
    $prospectos = [];
    while ($stmt->fetch()) {
        $prospectos[] = [
            'ID' => $id,
            'Tipo de Persona' => $tipo_persona,
            'RUT' => $rut_cliente,
            'Razón Social' => $razon_social,
            'Rubro' => $rubro,
            'Email' => $email,
            'Teléfono Celular' => $telefono_celular,
            'Fecha Registro' => $fecha_registro,
            'Ejecutivo' => $ejecutivo_nombre_usuario
        ];
    }
    
    $stmt->close();

} catch (Exception $e) {
    header('Content-Type: text/plain');
    echo "Error en la exportación: " . $e->getMessage();
    exit;
}

// Nombre del archivo para la descarga
$fecha_actual = date('Y-m-d');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.csv";

// Headers para descarga de archivo CSV
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Crear el contenido CSV
$output = fopen('php://output', 'w');

// Escribir BOM para UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Escribir encabezados
if (!empty($prospectos)) {
    fputcsv($output, array_keys($prospectos[0]), ';');
    
    // Escribir datos
    foreach ($prospectos as $prospecto) {
        fputcsv($output, $prospecto, ';');
    }
} else {
    fputcsv($output, ['Mensaje'], ';');
    fputcsv($output, ['No se encontraron prospectos para exportar'], ';');
}

fclose($output);
exit;
?>
