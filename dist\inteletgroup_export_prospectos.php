<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación y permisos de administrador
// Solo permitir acceso a usuarios de inteletGroup
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: login.php?error=' . $errorMsg);
    exit;
}

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// DEBUG: Punto de prueba muy temprano
echo "DEBUG: Archivo iniciado<br>";

// Incluir archivos necesarios
try {
    require_once 'cache_utils.php';
    echo "DEBUG: cache_utils.php incluido<br>";
} catch (Exception $e) {
    die("Error incluyendo cache_utils.php: " . $e->getMessage());
}

try {
    require_once 'con_db.php';
    echo "DEBUG: con_db.php incluido<br>";
} catch (Exception $e) {
    error_log("ERROR: Excepción al incluir con_db.php: " . $e->getMessage());
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Aplicar headers anti-caché
no_cache_headers();
echo "DEBUG: Headers aplicados<br>";

// Verificar conexión a base de datos
if (!isset($mysqli)) {
    error_log("ERROR: Variable mysqli no está definida después de incluir con_db.php");
    die("Error de conexión a la base de datos. Variable mysqli no definida.");
}

if ($mysqli->connect_error) {
    error_log("ERROR: Error de conexión MySQL: " . $mysqli->connect_error);
    die("Error de conexión a la base de datos: " . $mysqli->connect_error);
}

error_log("DEBUG: Conexión a BD establecida correctamente");

// DEBUG: Punto de prueba temprano
echo "DEBUG: Archivo de exportación iniciado correctamente<br>";
echo "Usuario ID: " . $_SESSION['usuario_id'] . "<br>";
echo "Proyecto: " . $_SESSION['proyecto'] . "<br>";
die("DEBUG: Deteniendo aquí para prueba");

// Obtener parámetros de filtros desde GET o POST
$request_data = array_merge($_GET, $_POST);
$filtro_ejecutivo = $request_data['ejecutivo'] ?? 'todos';
$filtro_periodo = $request_data['periodo'] ?? 'año';
$filtro_fecha_inicio = $request_data['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $request_data['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

error_log("INFO: Exportando prospectos con filtros - Ejecutivo: $filtro_ejecutivo, Periodo: $filtro_periodo");
error_log("INFO: Fechas - Inicio: $filtro_fecha_inicio, Fin: $filtro_fecha_fin");
error_log("INFO: WHERE clause: $where_clause");

// Consulta simplificada para obtener TODOS los prospectos
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.direccion_comercial, p.numero_pos, p.tipo_cuenta,
        p.numero_cuenta_bancaria, p.dias_atencion, p.horario_atencion, p.contrata_boleta, 
        p.competencia_actual, p.fecha_registro, p.fecha_actualizacion, p.usuario_id, 
        p.nombre_ejecutivo, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    WHERE " . $where_clause . "
    ORDER BY p.fecha_registro DESC";

try {
    $stmt = $mysqli->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparando consulta: " . $mysqli->error);
    }

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    if (!$stmt->execute()) {
        throw new Exception("Error ejecutando consulta: " . $stmt->error);
    }
} catch (Exception $e) {
    error_log("ERROR EN EXPORTACIÓN: " . $e->getMessage());
    header('Content-Type: text/plain');
    echo "Error en la exportación: " . $e->getMessage();
    exit;
}

// Usar bind_result() directamente para compatibilidad con PHP 7.3.33
error_log("DEBUG: Usando bind_result() para compatibilidad con PHP 7.3.33");

// Definir variables para bind_result
$id = $tipo_persona = $rut_cliente = $razon_social = $rubro = $email = $telefono_celular =
$direccion_comercial = $numero_pos = $tipo_cuenta = $numero_cuenta_bancaria = $dias_atencion =
$horario_atencion = $contrata_boleta = $competencia_actual = $fecha_registro = $fecha_actualizacion = $usuario_id =
$nombre_ejecutivo = $estado = $ejecutivo_nombre_usuario = null;

$stmt->bind_result(
    $id, $tipo_persona, $rut_cliente, $razon_social, $rubro, $email, $telefono_celular,
    $direccion_comercial, $numero_pos, $tipo_cuenta, $numero_cuenta_bancaria, $dias_atencion,
    $horario_atencion, $contrata_boleta, $competencia_actual, $fecha_registro, $fecha_actualizacion, $usuario_id,
    $nombre_ejecutivo, $estado, $ejecutivo_nombre_usuario
);

// Crear array para almacenar resultados
$prospectos = [];
while ($stmt->fetch()) {
        $prospectos[] = [
            'ID' => $id,
            'Tipo de Persona' => $tipo_persona,
            'RUT' => $rut_cliente,
            'Razón Social' => $razon_social,
            'Rubro' => $rubro,
            'Email' => $email,
            'Teléfono Celular' => $telefono_celular,
            'Dirección Comercial' => $direccion_comercial,
            'Número POS' => $numero_pos,
            'Tipo Cuenta' => $tipo_cuenta,
            'Número Cuenta Bancaria' => $numero_cuenta_bancaria,
            'Días Atención' => $dias_atencion,
            'Horario Atención' => $horario_atencion,
            'Contrata Boleta' => $contrata_boleta,
            'Competencia Actual' => $competencia_actual,
            'Fecha Registro' => $fecha_registro,
            'Fecha Actualización' => $fecha_actualizacion,
            'Usuario ID' => $usuario_id,
            'Nombre Ejecutivo' => $nombre_ejecutivo,
            'Estado' => $estado,
            'Ejecutivo' => $ejecutivo_nombre_usuario
        ];
    }

$stmt->close();

// Nombre del archivo para la descarga
$fecha_actual = date('Y-m-d');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.csv";

// Configurar encabezados para descarga de CSV
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');

// Crear el recurso de salida para escribir el CSV
$output = fopen('php://output', 'w');

// Configurar para que funcione con caracteres especiales (acentos, etc)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Escribir encabezados de las columnas
if (!empty($prospectos)) {
    fputcsv($output, array_keys($prospectos[0]));
    
    // Escribir datos
    foreach ($prospectos as $prospecto) {
        fputcsv($output, $prospecto);
    }
}

fclose($output);
exit;
?>