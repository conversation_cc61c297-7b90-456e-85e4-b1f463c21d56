<?php
// Script de diagnóstico para problemas de InteletGroup Documentos
ini_set('display_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die("Error: Usuario no autenticado o sin permisos para InteletGroup.");
}

require_once 'con_db.php';

$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';

echo "<!DOCTYPE html>";
echo "<html><head><title>Diagnóstico InteletGroup Documentos</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .ok{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";
echo "</head><body>";

echo "<h1>Diagnóstico InteletGroup Documentos</h1>";
echo "<p><strong>Usuario:</strong> $nombre_usuario (ID: $usuario_id)</p>";
echo "<p><strong>Proyecto:</strong> " . $_SESSION['proyecto'] . "</p>";
echo "<hr>";

// 1. Verificar conexión a base de datos
echo "<h2>1. Conexión a Base de Datos</h2>";
if (isset($mysqli) && $mysqli->ping()) {
    echo "<p class='ok'>✓ Conexión a base de datos OK</p>";
} else {
    echo "<p class='error'>✗ Error de conexión a base de datos</p>";
    die();
}

// 2. Verificar tablas
echo "<h2>2. Verificación de Tablas</h2>";
$tablas_requeridas = [
    'tb_inteletgroup_prospectos',
    'tb_inteletgroup_documentos', 
    'tb_inteletgroup_tipos_documento',
    'tb_inteletgroup_documento_checklist'
];

foreach ($tablas_requeridas as $tabla) {
    $result = $mysqli->query("SHOW TABLES LIKE '$tabla'");
    if ($result && $result->num_rows > 0) {
        echo "<p class='ok'>✓ Tabla $tabla existe</p>";
        
        // Mostrar información adicional
        $count_result = $mysqli->query("SELECT COUNT(*) as count FROM $tabla");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p>&nbsp;&nbsp;&nbsp;Registros: $count</p>";
        }
    } else {
        echo "<p class='error'>✗ Tabla $tabla NO existe</p>";
    }
}

// 3. Verificar collations
echo "<h2>3. Verificación de Collations</h2>";
$collation_query = "
    SELECT table_name, table_collation 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name IN ('tb_inteletgroup_prospectos', 'tb_inteletgroup_tipos_documento', 'tb_inteletgroup_documentos')
";

$result = $mysqli->query($collation_query);
if ($result) {
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>Tabla</th><th>Collation</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['table_name']}</td><td>{$row['table_collation']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>Error al verificar collations</p>";
}

// 4. Verificar prospectos del usuario
echo "<h2>4. Prospectos del Usuario</h2>";
$stmt = $mysqli->prepare("SELECT id, rut_cliente, razon_social, tipo_persona, fecha_registro FROM tb_inteletgroup_prospectos WHERE usuario_id = ? ORDER BY fecha_registro DESC");
if ($stmt) {
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();

    // Usar bind_result() en lugar de get_result() para compatibilidad con PHP 7.3
    $id = $rut_cliente = $razon_social = $tipo_persona = $fecha_registro = null;
    $stmt->bind_result($id, $rut_cliente, $razon_social, $tipo_persona, $fecha_registro);

    $count = 0;
    $prospectos = [];
    while ($stmt->fetch()) {
        $count++;
        $prospectos[] = [
            'id' => $id,
            'rut_cliente' => $rut_cliente,
            'razon_social' => $razon_social,
            'tipo_persona' => $tipo_persona,
            'fecha_registro' => $fecha_registro
        ];
    }

    if ($count > 0) {
        echo "<p class='ok'>✓ Encontrados $count prospectos</p>";
        echo "<table border='1' style='border-collapse:collapse;'>";
        echo "<tr><th>ID</th><th>RUT</th><th>Razón Social</th><th>Tipo Persona</th><th>Fecha Registro</th></tr>";
        foreach ($prospectos as $prospecto) {
            echo "<tr>";
            echo "<td>{$prospecto['id']}</td>";
            echo "<td>{$prospecto['rut_cliente']}</td>";
            echo "<td>{$prospecto['razon_social']}</td>";
            echo "<td>{$prospecto['tipo_persona']}</td>";
            echo "<td>{$prospecto['fecha_registro']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>✗ No se encontraron prospectos para este usuario</p>";
    }
    $stmt->close();
} else {
    echo "<p class='error'>Error al preparar consulta de prospectos</p>";
}

// 5. Probar consulta con JOINs
echo "<h2>5. Prueba de Consulta con JOINs</h2>";
try {
    $test_query = "
        SELECT p.id, p.rut_cliente, p.razon_social, COUNT(d.id) as docs
        FROM tb_inteletgroup_prospectos p
        LEFT JOIN tb_inteletgroup_documentos d ON p.id = d.prospecto_id
        WHERE p.usuario_id = ?
        GROUP BY p.id, p.rut_cliente, p.razon_social
        LIMIT 3
    ";
    
    $stmt = $mysqli->prepare($test_query);
    if ($stmt) {
        $stmt->bind_param("i", $usuario_id);
        $stmt->execute();
        // Usar bind_result() en lugar de get_result() para compatibilidad con PHP 7.3
        $id = $rut_cliente = $razon_social = $docs = null;
        $stmt->bind_result($id, $rut_cliente, $razon_social, $docs);

        echo "<p class='ok'>✓ Consulta básica con JOIN funciona</p>";
        echo "<p>Resultados:</p>";
        echo "<table border='1' style='border-collapse:collapse;'>";
        echo "<tr><th>ID</th><th>RUT</th><th>Razón Social</th><th>Docs</th></tr>";
        while ($stmt->fetch()) {
            echo "<tr>";
            echo "<td>$id</td>";
            echo "<td>$rut_cliente</td>";
            echo "<td>$razon_social</td>";
            echo "<td>$docs</td>";
            echo "</tr>";
        }
        echo "</table>";
        $stmt->close();
    } else {
        echo "<p class='error'>✗ Error al preparar consulta con JOIN</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error en consulta con JOIN: " . $e->getMessage() . "</p>";
}

// 6. Probar consulta compleja con tipos de documento
echo "<h2>6. Prueba de Consulta Compleja</h2>";
try {
    $complex_query = "
        SELECT p.id, p.rut_cliente, p.razon_social, p.tipo_persona, COUNT(td.id) as tipos_disponibles
        FROM tb_inteletgroup_prospectos p
        LEFT JOIN tb_inteletgroup_tipos_documento td ON 
            (td.tipo_persona COLLATE utf8mb4_spanish_ci = p.tipo_persona COLLATE utf8mb4_spanish_ci 
             OR td.tipo_persona = 'Ambos')
            AND td.estado = 'Activo'
        WHERE p.usuario_id = ?
        GROUP BY p.id, p.rut_cliente, p.razon_social, p.tipo_persona
        LIMIT 3
    ";
    
    $stmt = $mysqli->prepare($complex_query);
    if ($stmt) {
        $stmt->bind_param("i", $usuario_id);
        $stmt->execute();

        // Usar bind_result() en lugar de get_result() para compatibilidad con PHP 7.3
        $id = $rut_cliente = $razon_social = $tipo_persona = $tipos_disponibles = null;
        $stmt->bind_result($id, $rut_cliente, $razon_social, $tipo_persona, $tipos_disponibles);

        echo "<p class='ok'>✓ Consulta compleja con COLLATE funciona</p>";
        echo "<p>Resultados:</p>";
        echo "<table border='1' style='border-collapse:collapse;'>";
        echo "<tr><th>ID</th><th>RUT</th><th>Razón Social</th><th>Tipo Persona</th><th>Tipos Disponibles</th></tr>";
        while ($stmt->fetch()) {
            echo "<tr>";
            echo "<td>$id</td>";
            echo "<td>$rut_cliente</td>";
            echo "<td>$razon_social</td>";
            echo "<td>$tipo_persona</td>";
            echo "<td>$tipos_disponibles</td>";
            echo "</tr>";
        }
        echo "</table>";
        $stmt->close();
    } else {
        echo "<p class='error'>✗ Error al preparar consulta compleja: " . $mysqli->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error en consulta compleja: " . $e->getMessage() . "</p>";
}

// 7. Información del sistema
echo "<h2>7. Información del Sistema</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>MySQL Version:</strong> " . $mysqli->server_info . "</p>";
echo "<p><strong>Character Set:</strong> " . $mysqli->character_set_name() . "</p>";

echo "<hr>";
echo "<p><a href='inteletgroup_documentos_enhanced.php'>← Volver a Gestión de Documentos</a></p>";
echo "</body></html>";
?>
