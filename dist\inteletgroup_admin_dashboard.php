<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación y permisos de administrador
// Solo permitir acceso a usuarios de inteletGroup
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    // Si es Oriana (ID: 4), redirigir a su panel correspondiente
    if (isset($_SESSION['usuario_id']) && $_SESSION['usuario_id'] == 4) {
        header('Location: form_experian2.php');
        exit;
    }

    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: login.php?error=' . $errorMsg);
    exit;
}

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// Incluir archivos necesarios
require_once 'cache_utils.php';

try {
    require_once 'con_db.php';
} catch (Exception $e) {
    error_log("ERROR: Excepción al incluir con_db.php: " . $e->getMessage());
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Aplicar headers anti-caché
no_cache_headers();

// Verificar conexión a base de datos
if (!isset($mysqli)) {
    error_log("ERROR: Variable mysqli no está definida después de incluir con_db.php");
    die("Error de conexión a la base de datos. Variable mysqli no definida.");
}

if ($mysqli->connect_error) {
    error_log("ERROR: Error de conexión MySQL: " . $mysqli->connect_error);
    die("Error de conexión a la base de datos: " . $mysqli->connect_error);
}

error_log("DEBUG: Conexión a BD establecida correctamente");

// Verificar si es administrador (aquí puedes ajustar la lógica según tu sistema de roles)
$es_admin = true; // Por ahora lo dejamos en true, pero deberías verificar el rol del usuario

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$usuario_logueado_nombre = $_SESSION['nombre_usuario'] ?? 'Usuario';  // Variable protegida para el usuario logueado
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';

// Verificar información actual del usuario en la base de datos para asegurar datos correctos
try {
    $stmt = $mysqli->prepare("SELECT id, correo, nombre_usuario, proyecto, rol FROM tb_experian_usuarios WHERE id = ?");
    if (!$stmt) {
        throw new Exception("Error preparando consulta: " . $mysqli->error);
    }

    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();

    // Usar bind_result() en lugar de get_result() para compatibilidad con PHP 7.3
    $db_id = $db_correo = $db_nombre_usuario = $db_proyecto = $db_rol = null;
    $stmt->bind_result($db_id, $db_correo, $db_nombre_usuario, $db_proyecto, $db_rol);

    if ($stmt->fetch()) {
        // Usar información actualizada de la base de datos en lugar de la sesión
        $usuario_logueado_nombre = $db_nombre_usuario;  // Variable protegida
        $proyecto_db = $db_proyecto;
        $rol_db = $db_rol;

        // DEBUG: Log de información obtenida
        error_log("DEBUG DASHBOARD: Usuario ID=$usuario_id, Nombre BD='$db_nombre_usuario', Proyecto BD='$db_proyecto'");
        error_log("DEBUG DASHBOARD: Sesión nombre='" . ($_SESSION['nombre_usuario'] ?? 'N/A') . "', Sesión proyecto='" . ($_SESSION['proyecto'] ?? 'N/A') . "'");
    } else {
        error_log("ERROR: No se encontró usuario con ID " . $usuario_id . " en la base de datos");
    }
    $stmt->close();
} catch (Exception $e) {
    error_log("ERROR: Excepción en consulta de usuario: " . $e->getMessage());
    $usuario_logueado_nombre = $_SESSION['nombre_usuario'] ?? 'Usuario';
}

// Obtener parámetros de filtros
$filtro_ejecutivo = $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_GET['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Obtener lista de ejecutivos - SIMPLIFICADO
$ejecutivos = [];
error_log("DEBUG: Obteniendo lista de ejecutivos...");

// Inicializar estadísticas básicas
$stats = [
    'total_prospectos' => 0,
    'total_documentos' => 0,
    'prospectos_completos' => 0,
    'completitud_promedio' => 0,
    'prospectos_por_tipo' => [],
    'documentos_por_tipo' => [],
    'prospectos_por_ejecutivo' => [],
    'evolucion_mensual' => []
];

// Lista de prospectos simplificada
$prospectos_lista = [];

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Log de filtros aplicados
error_log("INFO: Filtros - Ejecutivo: $filtro_ejecutivo, Periodo: $filtro_periodo");
error_log("INFO: Fechas - Inicio: $filtro_fecha_inicio, Fin: $filtro_fecha_fin");
error_log("INFO: WHERE clause: $where_clause");

// Obtener estadísticas generales
$stats = [
    'total_prospectos' => 0,
    'total_documentos' => 0,
    'prospectos_completos' => 0,
    'completitud_promedio' => 0,
    'prospectos_por_tipo' => [],
    'documentos_por_tipo' => [],
    'prospectos_por_ejecutivo' => [],
    'evolucion_mensual' => []
];

// Total de prospectos
$query = "
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup'" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
";

error_log("DEBUG: Ejecutando consulta de prospectos: $query");

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $prosp_params = [];
    $prosp_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $prosp_params[] = $filtro_ejecutivo;
        $prosp_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $prosp_params[] = $filtro_fecha_inicio;
        $prosp_params[] = $filtro_fecha_fin;
        $prosp_types .= "ss";
    }
    
    if (!empty($prosp_params)) {
        $stmt->bind_param($prosp_types, ...$prosp_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($total_prospectos);
    if ($stmt->fetch()) {
        $stats['total_prospectos'] = $total_prospectos;
    }
    $stmt->close();
    error_log("INFO: Total prospectos obtenido: " . $stats['total_prospectos']);
} else {
    error_log("ERROR: Error en consulta de prospectos: " . $mysqli->error);
}

// Total de documentos
$query = "
    SELECT COUNT(DISTINCT d.id) as total_documentos 
    FROM tb_inteletgroup_documentos d
    INNER JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE d.estado = 'Activo' AND u.proyecto = 'inteletGroup'" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $doc_params = [];
    $doc_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $doc_params[] = $filtro_ejecutivo;
        $doc_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $doc_params[] = $filtro_fecha_inicio;
        $doc_params[] = $filtro_fecha_fin;
        $doc_types .= "ss";
    }
    
    if (!empty($doc_params)) {
        $stmt->bind_param($doc_types, ...$doc_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($total_documentos);
    if ($stmt->fetch()) {
        $stats['total_documentos'] = $total_documentos;
    }
    $stmt->close();
    error_log("INFO: Total documentos obtenido: " . $stats['total_documentos']);
} else {
    error_log("ERROR: Error en consulta de documentos: " . $mysqli->error);
}

// Completitud promedio
$completitud_promedio = 0;
$query = "
    SELECT AVG(completitud) as promedio
    FROM (
        SELECT 
            p.id,
            (COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) / 
             NULLIF(COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END), 0) * 100) as completitud
        FROM tb_inteletgroup_prospectos p
        INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
        LEFT JOIN tb_inteletgroup_tipos_documento td ON
            (td.tipo_persona = p.tipo_persona COLLATE utf8mb4_spanish_ci 
             OR td.tipo_persona = 'Ambos' COLLATE utf8mb4_spanish_ci)
            AND td.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        LEFT JOIN tb_inteletgroup_documentos d ON
            p.id = d.prospecto_id
            AND d.tipo_documento_id = td.id
            AND d.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        WHERE u.proyecto = 'inteletGroup' COLLATE utf8mb4_spanish_ci" . 
        ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
        ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
        GROUP BY p.id
    ) as t
    WHERE completitud IS NOT NULL
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $compl_params = [];
    $compl_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $compl_params[] = $filtro_ejecutivo;
        $compl_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $compl_params[] = $filtro_fecha_inicio;
        $compl_params[] = $filtro_fecha_fin;
        $compl_types .= "ss";
    }
    
    if (!empty($compl_params)) {
        $stmt->bind_param($compl_types, ...$compl_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($completitud_promedio);
    if ($stmt->fetch()) {
        $stats['completitud_promedio'] = round($completitud_promedio, 1);
    }
    $stmt->close();
    error_log("INFO: Completitud promedio: " . $stats['completitud_promedio']);
} else {
    error_log("ERROR: Error en consulta de completitud promedio: " . $mysqli->error);
    $stats['completitud_promedio'] = 0;
}

// Obtener número de prospectos con 100% de completitud (todos los documentos obligatorios)
$prospectos_completos = 0;
$query = "
    SELECT COUNT(*) as completos FROM (
        SELECT 
            p.id,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as completados,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
        FROM tb_inteletgroup_prospectos p
        INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
        LEFT JOIN tb_inteletgroup_tipos_documento td ON
            (td.tipo_persona = p.tipo_persona COLLATE utf8mb4_spanish_ci
             OR td.tipo_persona = 'Ambos' COLLATE utf8mb4_spanish_ci)
            AND td.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        LEFT JOIN tb_inteletgroup_documentos d ON
            p.id = d.prospecto_id
            AND d.tipo_documento_id = td.id
            AND d.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        WHERE u.proyecto = 'inteletGroup' COLLATE utf8mb4_spanish_ci" . 
        ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
        ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
        GROUP BY p.id
        HAVING completados = total_obligatorios AND total_obligatorios > 0
    ) as t
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stmt->bind_result($prospectos_completos);
    if ($stmt->fetch()) {
        $stats['prospectos_completos'] = $prospectos_completos;
    }
    $stmt->close();
} else {
    error_log("ERROR: No se pudo preparar consulta de prospectos completos: " . $mysqli->error);
    $stats['prospectos_completos'] = 0;
}

// Prospectos por tipo de persona
error_log("DEBUG: Obteniendo prospectos por tipo de persona...");
$query = "
    SELECT 
        p.tipo_persona,
        COUNT(DISTINCT p.id) as total
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' " . (!empty($where_clause) ? " AND $where_clause" : "") . "
    GROUP BY p.tipo_persona
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stmt->bind_result($tipo_persona, $cantidad);
    while ($stmt->fetch()) {
        $stats['prospectos_por_tipo'][$tipo_persona] = $cantidad;
    }
    $stmt->close();
    error_log("DEBUG: Tipos encontrados: " . print_r($stats['prospectos_por_tipo'], true));
} else {
    error_log("ERROR: No se pudo preparar consulta por tipo: " . $mysqli->error);
}

// Prospectos por ejecutivo (top 10)
$query = "
    SELECT
        u.nombre_usuario,
        COUNT(DISTINCT p.id) as total_prospectos,
        COUNT(DISTINCT d.id) as total_documentos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    LEFT JOIN tb_inteletgroup_documentos d ON p.id = d.prospecto_id AND d.estado = 'Activo'
    WHERE u.proyecto = 'inteletGroup'" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
    GROUP BY u.id, u.nombre_usuario
    ORDER BY total_prospectos DESC
    LIMIT 10
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $exec_params = [];
    $exec_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $exec_params[] = $filtro_ejecutivo;
        $exec_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $exec_params[] = $filtro_fecha_inicio;
        $exec_params[] = $filtro_fecha_fin;
        $exec_types .= "ss";
    }
    
    if (!empty($exec_params)) {
        $stmt->bind_param($exec_types, ...$exec_params);
    }
    
    $stmt->execute();
    $ejecutivo_nombre = $total_prosp = $total_docs = null;
    $stmt->bind_result($ejecutivo_nombre, $total_prosp, $total_docs);
    while ($stmt->fetch()) {
        $stats['prospectos_por_ejecutivo'][] = [
            'nombre' => $ejecutivo_nombre,
            'prospectos' => $total_prosp,
            'documentos' => $total_docs
        ];
    }
    $stmt->close();
}

// Registrar resultados
error_log("INFO: Prospectos por ejecutivo: " . count($stats['prospectos_por_ejecutivo']));

// Evolución diaria (últimos 30 días)
$query = "
    SELECT 
        DATE(p.fecha_registro) as dia,
        COUNT(DISTINCT p.id) as prospectos,
        COUNT(DISTINCT d.id) as documentos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    LEFT JOIN tb_inteletgroup_documentos d ON p.id = d.prospecto_id AND d.estado = 'Activo'
    WHERE p.fecha_registro >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    AND u.proyecto = 'inteletGroup'
    " . ($filtro_ejecutivo !== 'todos' ? "AND p.usuario_id = ?" : "") . "
    GROUP BY dia
    ORDER BY dia ASC
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    if ($filtro_ejecutivo !== 'todos') {
        $stmt->bind_param("i", $filtro_ejecutivo);
    }
    $stmt->execute();
    $stmt->bind_result($dia, $prospectos, $documentos);
    while ($stmt->fetch()) {
        $stats['evolucion_mensual'][] = [
            'dia' => $dia,
            'prospectos' => $prospectos,
            'documentos' => $documentos
        ];
    }
    $stmt->close();
    error_log("INFO: Datos de evolución obtenidos: " . count($stats['evolucion_mensual']));
}

// Obtener lista de todos los prospectos con detalles
$prospectos_lista = [];
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1" .
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") .
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
";

error_log("INFO: Ejecutando consulta de prospectos");
error_log("Query: " . $query);
error_log("Filtro ejecutivo: " . $filtro_ejecutivo);
error_log("Filtro fecha inicio: " . $filtro_fecha_inicio);
error_log("Filtro fecha fin: " . $filtro_fecha_fin);

// Modificamos la consulta para hacerla compatible con servidores MySQL sin mysqlnd
// Necesitamos usar bind_result en lugar de get_result

// Primero obtenemos los datos básicos
$query_basic = "
    SELECT p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
           p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    WHERE 1=1" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
    ORDER BY p.fecha_registro DESC
";

$stmt = $mysqli->prepare($query_basic);
if ($stmt) {
    $lista_params = [];
    $lista_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $lista_params[] = $filtro_ejecutivo;
        $lista_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $lista_params[] = $filtro_fecha_inicio;
        $lista_params[] = $filtro_fecha_fin;
        $lista_types .= "ss";
    }
    
    if (!empty($lista_params)) {
        $stmt->bind_param($lista_types, ...$lista_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($id, $tipo_persona, $rut_cliente, $razon_social, $rubro, $email, $telefono_celular, $fecha_registro, $ejecutivo_nombre_usuario);
    
    $prospectos_temp = [];
    while ($stmt->fetch()) {
        $prospectos_temp[$id] = [
            'id' => $id,
            'tipo_persona' => $tipo_persona,
            'rut_cliente' => $rut_cliente,
            'razon_social' => $razon_social,
            'rubro' => $rubro,
            'email' => $email,
            'telefono_celular' => $telefono_celular,
            'fecha_registro' => $fecha_registro,
            'ejecutivo_nombre_usuario' => $ejecutivo_nombre_usuario ?: 'Sin asignar',
            'ejecutivo_nombre_completo' => $ejecutivo_nombre_usuario ?: 'Sin asignar',
            'total_documentos' => 0,
            'obligatorios_completados' => 0,
            'total_obligatorios' => 0,
            'porcentaje_completado' => 0
        ];
    }
    $stmt->close();
    
    // Ahora obtenemos los datos de documentos por cada prospecto
    if (!empty($prospectos_temp)) {
        foreach ($prospectos_temp as $prospecto_id => $prospecto_data) {
            $query_docs = "
                SELECT
                    COUNT(DISTINCT d.id),
                    COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END),
                    COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END)
                FROM tb_inteletgroup_prospectos p
                LEFT JOIN tb_inteletgroup_tipos_documento td ON
                    (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci OR td.tipo_persona = 'Ambos')
                    AND td.estado = 'Activo'
                LEFT JOIN tb_inteletgroup_documentos d ON
                    p.id = d.prospecto_id
                    AND d.tipo_documento_id = td.id
                    AND d.estado = 'Activo'
                WHERE p.id = ?
            ";
            
            $stmt_docs = $mysqli->prepare($query_docs);
            if ($stmt_docs) {
                $stmt_docs->bind_param("i", $prospecto_id);
                $stmt_docs->execute();
                $stmt_docs->bind_result($total_docs, $obligatorios_comp, $total_oblig);
                
                if ($stmt_docs->fetch()) {
                    $porcentaje = $total_oblig > 0 ? round(($obligatorios_comp / $total_oblig) * 100) : 0;
                    
                    $prospectos_temp[$prospecto_id]['total_documentos'] = $total_docs;
                    $prospectos_temp[$prospecto_id]['obligatorios_completados'] = $obligatorios_comp;
                    $prospectos_temp[$prospecto_id]['total_obligatorios'] = $total_oblig;
                    $prospectos_temp[$prospecto_id]['porcentaje_completado'] = $porcentaje;
                }
                $stmt_docs->close();
            }
        }
        
        // Convertir de array asociativo a indexado
        $prospectos_lista = array_values($prospectos_temp);
    }
    $stmt->close();
    error_log("INFO: Prospectos obtenidos: " . count($prospectos_lista));
} else {
    error_log("ERROR: Error en consulta de prospectos: " . $mysqli->error);
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel Administrativo - InteletGroup</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <?php
    // Generar un timestamp para el versionado de recursos
    $version = time();
    ?>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs5/jq-3.6.0/dt-1.11.5/datatables.min.css"/>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/dashboard.css?v=<?php echo $version; ?>">
    <link rel="stylesheet" href="css/styles.css?v=<?php echo $version; ?>">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/inteletgroup_documentos_enhanced.css?v=<?php echo time(); ?>">
    
    <style>
        /* Reduce global zoom to 80% for smaller UI */
        html {
            zoom: 70%; /* Works in most modern browsers */
        }
        
        /* Eliminar márgenes laterales y hacer que el contenedor ocupe todo el ancho */
        body {
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 100% !important;
            padding-left: 15px;
            padding-right: 15px;
            margin-left: 0;
            margin-right: 0;
            width: 100%;
        }
        
        .row {
            margin-left: 0;
            margin-right: 0;
        }
        
        [class*="col-"] {
            padding-left: 8px;
            padding-right: 8px;
        }

        /* Estilos adicionales para el dashboard */
        .dashboard-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: all 0.3s ease;
            background: white;
            padding: 1.5rem;
            height: 100%;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .kpi-card {
            text-align: center;
            padding: 1.2rem 0.8rem; /* Reducido de 2rem 1rem */
        }

        .kpi-value {
            font-size: 2.4rem; /* Reducido de 3rem */
            font-weight: 700;
            color: var(--primary-dark);
            line-height: 1;
            margin-bottom: 0.3rem; /* Reducido de 0.5rem */
        }

        .kpi-label {
            color: var(--secondary-color);
            font-size: 0.8rem; /* Reducido de 0.875rem */
            text-transform: uppercase;
            letter-spacing: 0.4px; /* Reducido de 0.5px */
            margin-bottom: 0.2rem; /* Añadido para mejor espaciado */
        }

        .kpi-change {
            font-size: 0.8rem; /* Reducido de 0.875rem */
            font-weight: 600;
            margin-top: 0.3rem; /* Reducido de 0.5rem */
        }

        .kpi-change.positive {
            color: var(--success-color);
        }

        .kpi-change.negative {
            color: var(--danger-color);
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .filter-section {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
        }

        .table-container {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        /* Tabs personalizados */
        .nav-tabs {
            border-bottom: 2px solid #e5e7eb;
        }

        .nav-tabs .nav-link {
            color: var(--secondary-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            color: var(--primary-medium);
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-dark);
            background: none;
            border: none;
            border-bottom: 3px solid var(--primary-medium);
        }

        /* Badges mejorados */
        .badge-natural {
            background-color: #3b82f6;
            color: white;
        }

        .badge-juridica {
            background-color: #10b981;
            color: white;
        }

        /* Responsive tables */
        .table-responsive {
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        /* Iconos en estadísticas */
        .stat-icon {
            width: 40px; /* Reducido de 50px */
            height: 40px; /* Reducido de 50px */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.6rem; /* Reducido de 1rem */
            font-size: 1.2rem; /* Reducido de 1.5rem */
            color: white;
        }

        .stat-icon.blue {
            background: rgba(38, 153, 251, 0.1);
            color: var(--primary-blue);
        }

        .stat-icon.green {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-icon.yellow {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.purple {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1>
                        <i class="bi bi-speedometer2 me-3"></i>
                        Panel Administrativo
                    </h1>
                    <p class="mb-0 opacity-75">Vista global de prospectos y documentación</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="user-section d-flex justify-content-end align-items-center">
                        <a href="inteletgroup_documentos_enhanced.php" class="btn btn-light me-3">
                            <i class="bi bi-files me-2"></i>
                            Gestión Documentos
                        </a>
                        <div class="user-info-container me-3">
                            <div class="user-name"><?php
                                // Usar la variable protegida del usuario logueado
                                error_log("DEBUG HEADER: Mostrando usuario_logueado_nombre='$usuario_logueado_nombre'");
                                echo htmlspecialchars($usuario_logueado_nombre);
                            ?></div>
                            <div class="user-role">Administrador</div>
                            <!-- DEBUG INFO TEMPORAL -->
                            <div style="font-size: 10px; color: #fff; opacity: 0.7;">
                                DEBUG: ID=<?php echo $usuario_id; ?> | BD=<?php echo htmlspecialchars($db_nombre_usuario ?? 'N/A'); ?> | Sesión=<?php echo htmlspecialchars($_SESSION['nombre_usuario'] ?? 'N/A'); ?> | Logueado=<?php echo htmlspecialchars($usuario_logueado_nombre); ?>
                            </div>
                        </div>
                        <a href="logout.php" class="logout-btn" title="Cerrar sesión">
                            <i class="bi bi-box-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filtros -->
        <div class="filter-section">
            <form method="GET" id="filterForm">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">Ejecutivo</label>
                        <select name="ejecutivo" class="form-select">
                            <option value="todos" <?php echo $filtro_ejecutivo == 'todos' ? 'selected' : ''; ?>>Todos los ejecutivos</option>
                            <?php foreach ($ejecutivos as $ejecutivo): ?>
                                <option value="<?php echo $ejecutivo['id']; ?>" 
                                        <?php echo $filtro_ejecutivo == $ejecutivo['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($ejecutivo['nombre_completo']); ?> 
                                    (<?php echo $ejecutivo['total_prospectos']; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Periodo</label>
                        <select name="periodo" class="form-select">
                            <option value="hoy" <?php echo $filtro_periodo == 'hoy' ? 'selected' : ''; ?>>Hoy</option>
                            <option value="semana" <?php echo $filtro_periodo == 'semana' ? 'selected' : ''; ?>>Esta semana</option>
                            <option value="mes_actual" <?php echo $filtro_periodo == 'mes_actual' ? 'selected' : ''; ?>>Este mes</option>
                            <option value="trimestre" <?php echo $filtro_periodo == 'trimestre' ? 'selected' : ''; ?>>Este trimestre</option>
                            <option value="año" <?php echo $filtro_periodo == 'año' ? 'selected' : ''; ?>>Este año</option>
                            <option value="personalizado" <?php echo $filtro_periodo == 'personalizado' ? 'selected' : ''; ?>>Personalizado</option>
                        </select>
                    </div>
                    <div class="col-md-2" id="fecha_inicio_container" style="<?php echo $filtro_periodo == 'personalizado' ? '' : 'display:none;'; ?>">
                        <label class="form-label">Fecha inicio</label>
                        <input type="date" name="fecha_inicio" class="form-control" value="<?php echo $filtro_fecha_inicio; ?>">
                    </div>
                    <div class="col-md-2" id="fecha_fin_container" style="<?php echo $filtro_periodo == 'personalizado' ? '' : 'display:none;'; ?>">
                        <label class="form-label">Fecha fin</label>
                        <input type="date" name="fecha_fin" class="form-control" value="<?php echo $filtro_fecha_fin; ?>">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-funnel me-2"></i>
                            Aplicar filtros
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Primera fila: 4 KPIs -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon blue mx-auto">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="kpi-value"><?php echo number_format($stats['total_prospectos']); ?></div>
                    <div class="kpi-label">Total Prospectos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon green mx-auto">
                        <i class="bi bi-check-circle-fill"></i>
                    </div>
                    <div class="kpi-value"><?php echo number_format($stats['prospectos_completos']); ?></div>
                    <div class="kpi-label">Prospectos Completos</div>
                    <div class="kpi-change positive">
                        <?php 
                        $porcentaje_completos = $stats['total_prospectos'] > 0 
                            ? round(($stats['prospectos_completos'] / $stats['total_prospectos']) * 100, 1)
                            : 0;
                        echo $porcentaje_completos . '%';
                        ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon yellow mx-auto">
                        <i class="bi bi-file-earmark-text-fill"></i>
                    </div>
                    <div class="kpi-value"><?php echo number_format($stats['total_documentos']); ?></div>
                    <div class="kpi-label">Total Documentos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon purple mx-auto">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="kpi-value"><?php echo $stats['completitud_promedio']; ?>%</div>
                    <div class="kpi-label">Completitud Promedio</div>
                </div>
            </div>
        </div>

        <!-- Segunda fila: Gráficos -->
        <div class="row mb-4">
            <!-- Gráfico de evolución - 4 columnas -->
            <div class="col-md-4">
                <div class="dashboard-card h-100">
                    <h5 class="mb-3">
                        <i class="bi bi-bar-chart me-2"></i>
                        Evolución Diaria
                    </h5>
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="evolucionChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Gráfico de tipo de persona - 4 columnas -->
            <div class="col-md-4">
                <div class="dashboard-card h-100">
                    <h5 class="mb-3">
                        <i class="bi bi-pie-chart me-2"></i>
                        Distribución por Tipo
                    </h5>
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="tipoPersonaChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Gráfico de ejecutivos - 4 columnas -->
            <div class="col-md-4">
                <div class="dashboard-card h-100">
                    <h5 class="mb-3">
                        <i class="bi bi-people me-2"></i>
                        Top Ejecutivos
                    </h5>
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="ejecutivosChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabla de prospectos -->
        <div class="table-container">
            <h5 class="mb-4">
                <i class="bi bi-list-task me-2"></i>
                Lista de Prospectos
            </h5>
            
            <div class="table-responsive">
                <table id="prospectosTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Razón Social</th>
                            <th>RUT</th>
                            <th>Tipo</th>
                            <th>Ejecutivo</th>
                            <th>Documentos</th>
                            <th>Completitud</th>
                            <th>Fecha Registro</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($prospectos_lista as $prospecto): ?>
                            <tr>
                                <td><?php echo $prospecto['id']; ?></td>
                                <td><?php echo htmlspecialchars($prospecto['razon_social']); ?></td>
                                <td><?php echo htmlspecialchars($prospecto['rut_cliente']); ?></td>
                                <td>
                                    <span class="badge badge-<?php echo strtolower($prospecto['tipo_persona']); ?>">
                                        <?php echo $prospecto['tipo_persona']; ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($prospecto['ejecutivo_nombre_completo']); ?></td>
                                <td>
                                    <?php echo $prospecto['obligatorios_completados']; ?>/<?php echo $prospecto['total_obligatorios']; ?>
                                    <small class="text-muted">(Total: <?php echo $prospecto['total_documentos']; ?>)</small>
                                </td>
                                <td>
                                    <div class="progress-custom" style="height: 10px;">
                                        <div class="progress-bar-custom <?php 
                                            if ($prospecto['porcentaje_completado'] < 50) {
                                                echo 'red';
                                            } elseif ($prospecto['porcentaje_completado'] < 80) {
                                                echo 'yellow';
                                            } else {
                                                echo 'green';
                                            }
                                        ?>" style="width: <?php echo $prospecto['porcentaje_completado']; ?>%"></div>
                                    </div>
                                    <small class="text-muted"><?php echo $prospecto['porcentaje_completado']; ?>%</small>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($prospecto['fecha_registro'])); ?></td>
                                <td>
                                    <a href="inteletgroup_documentos_enhanced.php?prospecto_id=<?php echo $prospecto['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="Ver documentos">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/v/bs5/jq-3.6.0/dt-1.11.5/datatables.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom JS con versionado -->
    <script src="js/dashboard.js?v=<?php echo $version; ?>"></script>

    <script>
        // Inicializar DataTable
        $(document).ready(function() {
            $('#prospectosTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/es-ES.json'
                },
                order: [[7, 'desc']], // Ordenar por fecha de registro descendente
                pageLength: 25,
                responsive: true
            });
        });

        // Mostrar/ocultar campos de fecha personalizada
        document.querySelector('select[name="periodo"]').addEventListener('change', function() {
            const personalizado = this.value === 'personalizado';
            document.getElementById('fecha_inicio_container').style.display = personalizado ? 'block' : 'none';
            document.getElementById('fecha_fin_container').style.display = personalizado ? 'block' : 'none';
        });

        // Configuración de gráficos
        const chartColors = {
            primary: '#3b82f6',
            success: '#10b981',
            warning: '#f59e0b',
            danger: '#ef4444',
            secondary: '#6b7280',
            purple: '#8b5cf6'
        };

        // Gráfico de evolución diaria
        const evolucionData = <?php echo json_encode($stats['evolucion_mensual']); ?>;
        const evolucionCtx = document.getElementById('evolucionChart').getContext('2d');

        // Verificar si hay datos
        if (evolucionData && evolucionData.length > 0) {
            new Chart(evolucionCtx, {
                type: 'line',
                data: {
                    labels: evolucionData.map(item => {
                        const fecha = new Date(item.dia);
                        return fecha.getDate() + '/' + (fecha.getMonth() + 1);
                    }),
                    datasets: [{
                        label: 'Prospectos',
                        data: evolucionData.map(item => item.prospectos),
                        borderColor: chartColors.primary,
                        backgroundColor: chartColors.primary + '20',
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 8
                    }, {
                        label: 'Documentos',
                        data: evolucionData.map(item => item.documentos),
                        borderColor: chartColors.success,
                        backgroundColor: chartColors.success + '20',
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 8
                            }
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    const index = tooltipItems[0].dataIndex;
                                    return 'Fecha: ' + evolucionData[index].dia;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } else {
            // Mostrar mensaje si no hay datos
            document.getElementById('evolucionChart').parentNode.innerHTML = '<p class="text-center text-muted mt-5">No hay datos disponibles para el período seleccionado</p>';
        }

        // Gráfico de tipo de persona
        const tipoPersonaData = <?php echo json_encode($stats['prospectos_por_tipo']); ?>;
        const tipoPersonaCtx = document.getElementById('tipoPersonaChart').getContext('2d');

        // Verificar si hay datos
        if (tipoPersonaData && Object.keys(tipoPersonaData).length > 0) {
            new Chart(tipoPersonaCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(tipoPersonaData),
                    datasets: [{
                        data: Object.values(tipoPersonaData),
                        backgroundColor: [chartColors.primary, chartColors.success],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 8
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        } else {
            // Mostrar mensaje si no hay datos
            document.getElementById('tipoPersonaChart').parentNode.innerHTML = '<p class="text-center text-muted mt-5">No hay datos disponibles para el período seleccionado</p>';
        }

        // Gráfico de ejecutivos
        const ejecutivosData = <?php echo json_encode($stats['prospectos_por_ejecutivo']); ?>;
        const ejecutivosCtx = document.getElementById('ejecutivosChart').getContext('2d');

        // Verificar si hay datos
        if (ejecutivosData && ejecutivosData.length > 0) {
            new Chart(ejecutivosCtx, {
                type: 'bar',
                data: {
                    labels: ejecutivosData.map(item => item.nombre),
                    datasets: [{
                        label: 'Prospectos',
                        data: ejecutivosData.map(item => item.prospectos),
                        backgroundColor: chartColors.primary,
                        borderWidth: 1,
                        borderRadius: 4
                    }, {
                        label: 'Documentos',
                        data: ejecutivosData.map(item => item.documentos),
                        backgroundColor: chartColors.success,
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 8
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                drawBorder: false
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        } else {
            // Mostrar mensaje si no hay datos
            document.getElementById('ejecutivosChart').parentNode.innerHTML = '<p class="text-center text-muted mt-5">No hay datos disponibles para el período seleccionado</p>';
        }

        // Mejorar manejo de filtros para evitar problemas de caché
        function submitFilterForm() {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);

            // Agregar timestamp para evitar caché
            formData.append('_t', Date.now());

            // Construir URL con parámetros
            const params = new URLSearchParams();
            for (let [key, value] of formData.entries()) {
                params.append(key, value);
            }

            // Redirigir con los nuevos parámetros
            window.location.href = window.location.pathname + '?' + params.toString();
        }

        // Reemplazar los eventos onchange por eventos más robustos
        document.addEventListener('DOMContentLoaded', function() {
            const ejecutivoSelect = document.querySelector('select[name="ejecutivo"]');
            const periodoSelect = document.querySelector('select[name="periodo"]');

            if (ejecutivoSelect) {
                ejecutivoSelect.removeAttribute('onchange');
                ejecutivoSelect.addEventListener('change', function() {
                    submitFilterForm();
                });
            }

            if (periodoSelect) {
                periodoSelect.removeAttribute('onchange');
                periodoSelect.addEventListener('change', function() {
                    submitFilterForm();
                });
            }
        });
    </script>
</body>
</html>