<?php
// Script para revisar los logs más recientes
header('Content-Type: text/plain');

echo "=== LOGS DE DEPURACIÓN ===\n\n";

// Revisar error_log de PHP
$error_log_path = ini_get('error_log');
if ($error_log_path && file_exists($error_log_path)) {
    echo "Error log de PHP: $error_log_path\n";
    $lines = file($error_log_path);
    $recent_lines = array_slice($lines, -50); // Últimas 50 líneas
    
    echo "\n--- Últimas líneas del error log ---\n";
    foreach ($recent_lines as $line) {
        if (stripos($line, 'DEBUG') !== false || stripos($line, 'inteletgroup') !== false) {
            echo $line;
        }
    }
} else {
    echo "No se puede acceder al error log de PHP\n";
}

// Revisar logs locales
$local_logs = [
    'logs/db_errors.log',
    'logs/password_actions.log',
    'error_log'
];

foreach ($local_logs as $log_file) {
    if (file_exists($log_file)) {
        echo "\n\n=== $log_file ===\n";
        $lines = file($log_file);
        $recent_lines = array_slice($lines, -20); // Últimas 20 líneas
        foreach ($recent_lines as $line) {
            echo $line;
        }
    }
}

// Información de sesión actual
session_start();
echo "\n\n=== INFORMACIÓN DE SESIÓN ACTUAL ===\n";
echo "Session ID: " . session_id() . "\n";
echo "Usuario ID: " . ($_SESSION['usuario_id'] ?? 'NO SET') . "\n";
echo "Nombre Usuario: " . ($_SESSION['nombre_usuario'] ?? 'NO SET') . "\n";
echo "Proyecto: " . ($_SESSION['proyecto'] ?? 'NO SET') . "\n";
echo "Rol: " . ($_SESSION['rol'] ?? 'NO SET') . "\n";

echo "\n=== TODAS LAS VARIABLES DE SESIÓN ===\n";
print_r($_SESSION);
?>
